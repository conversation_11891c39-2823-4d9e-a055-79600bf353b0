import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const apiKey = process.env.OPENAI_API_KEY;
const mockMode = process.env.MOCK_OPENAI === 'true';

let openai: OpenAI | null = null;
if (apiKey && !mockMode) {
  openai = new OpenAI({ apiKey });
}

interface AdditionalInfoSuggestion {
  category: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
}

interface AdditionalInfoResponse {
  suggestions: AdditionalInfoSuggestion[];
  analysis: {
    productType: string;
    detectedFeatures: string[];
    recommendedSections: string[];
  };
}

// AI-powered function for intelligent additional info suggestions
async function generateIntelligentAdditionalInfoSuggestions(productName: string, category: string, existingContent: string): Promise<AdditionalInfoResponse> {
  if (!openai) {
    return generateIntelligentSuggestionsMock(productName, category, existingContent);
  }

  try {
    const prompt = `Analise o produto "${productName}" da categoria "${category}" e sugira seções de informações adicionais relevantes.

Conteúdo existente do produto: ${existingContent}

Instruções:
- Analise o tipo específico de produto e suas características
- Identifique que informações adicionais seriam mais úteis para este produto específico
- Considere o que os clientes precisariam saber sobre este produto
- Priorize seções baseadas na relevância para o produto específico
- Evite sugestões genéricas que se aplicam a qualquer produto

Responda com um JSON contendo:
{
  "suggestions": [
    {
      "category": "nome_da_categoria",
      "title": "Título da Seção",
      "description": "Descrição específica do que esta seção conteria para este produto",
      "priority": "high|medium|low"
    }
  ],
  "analysis": {
    "productType": "tipo específico do produto analisado",
    "detectedFeatures": ["característica1", "característica2"],
    "recommendedSections": ["categoria1", "categoria2"]
  }
}

Categorias disponíveis: specifications, care, compatibility, usage, safety, warranty, assembly, sizing`;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em análise de produtos e informações técnicas. Analise produtos e sugira informações adicionais relevantes e específicas."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.4,
      max_tokens: 800,
      response_format: { type: "json_object" },
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("Resposta vazia da API");
    }

    const parsedResponse = JSON.parse(content);

    // Validate and ensure proper structure
    if (!parsedResponse.suggestions || !Array.isArray(parsedResponse.suggestions)) {
      throw new Error("Formato de resposta inválido");
    }

    // Limit to 6 suggestions and ensure proper format
    const validSuggestions = parsedResponse.suggestions
      .slice(0, 6)
      .filter((s: any) => s.category && s.title && s.description && s.priority)
      .map((s: any) => ({
        category: s.category,
        title: s.title,
        description: s.description,
        priority: ['high', 'medium', 'low'].includes(s.priority) ? s.priority : 'medium'
      }));

    return {
      suggestions: validSuggestions,
      analysis: {
        productType: parsedResponse.analysis?.productType || category,
        detectedFeatures: Array.isArray(parsedResponse.analysis?.detectedFeatures) ? parsedResponse.analysis.detectedFeatures : [],
        recommendedSections: Array.isArray(parsedResponse.analysis?.recommendedSections) ? parsedResponse.analysis.recommendedSections : validSuggestions.filter(s => s.priority === 'high').map(s => s.category)
      }
    };

  } catch (error) {
    console.error('Erro ao gerar sugestões inteligentes:', error);
    return generateIntelligentSuggestionsMock(productName, category, existingContent);
  }
}

// Intelligent mock function for fallback
function generateIntelligentSuggestionsMock(productName: string, category: string, existingContent: string): AdditionalInfoResponse {
  const productLower = productName.toLowerCase();
  const categoryLower = category.toLowerCase();

  // Intelligent feature detection
  const detectedFeatures: string[] = [];
  if (existingContent.includes('resistente') || existingContent.includes('durável')) detectedFeatures.push('durabilidade');
  if (existingContent.includes('leve') || existingContent.includes('peso')) detectedFeatures.push('peso otimizado');
  if (existingContent.includes('impermeável') || existingContent.includes('água')) detectedFeatures.push('proteção contra água');
  if (existingContent.includes('ergonómico') || existingContent.includes('conforto')) detectedFeatures.push('ergonomia');
  if (existingContent.includes('tecnologia') || existingContent.includes('digital')) detectedFeatures.push('tecnologia avançada');
  if (existingContent.includes('sustentável') || existingContent.includes('eco')) detectedFeatures.push('sustentabilidade');

  const suggestions: AdditionalInfoSuggestion[] = [];

  // Always include specifications as it's universally relevant
  suggestions.push({
    category: 'specifications',
    title: 'Especificações Técnicas',
    description: `Dimensões, materiais e características técnicas específicas do ${productName}`,
    priority: 'high'
  });

  // Electronics and technology products
  if (categoryLower.includes('eletrónica') || categoryLower.includes('eletrônica') ||
      categoryLower.includes('tecnologia') || productLower.includes('smartphone') ||
      productLower.includes('tablet') || productLower.includes('computador')) {

    suggestions.push(
      {
        category: 'compatibility',
        title: 'Compatibilidade e Conectividade',
        description: `Dispositivos compatíveis, sistemas suportados e opções de conectividade do ${productName}`,
        priority: 'high'
      },
      {
        category: 'usage',
        title: 'Dicas de Otimização',
        description: `Truques e configurações para maximizar o desempenho do ${productName}`,
        priority: 'medium'
      },
      {
        category: 'safety',
        title: 'Certificações e Segurança',
        description: `Certificações de segurança, proteção de dados e normas específicas para ${productName}`,
        priority: 'medium'
      }
    );
  }

  // Clothing and textiles
  else if (categoryLower.includes('roupa') || categoryLower.includes('vestuário') ||
           categoryLower.includes('têxtil') || productLower.includes('camisa') ||
           productLower.includes('calças') || productLower.includes('vestido')) {

    suggestions.push(
      {
        category: 'sizing',
        title: 'Guia de Tamanhos',
        description: `Tabela de medidas detalhada e dicas para escolher o tamanho ideal do ${productName}`,
        priority: 'high'
      },
      {
        category: 'care',
        title: 'Cuidados Específicos',
        description: `Instruções de lavagem, secagem e conservação específicas para o ${productName}`,
        priority: 'high'
      },
      {
        category: 'usage',
        title: 'Styling e Combinações',
        description: `Sugestões de como usar e combinar o ${productName} para diferentes ocasiões`,
        priority: 'medium'
      }
    );
  }

  // Furniture and home products
  else if (categoryLower.includes('mobiliário') || categoryLower.includes('móveis') ||
           categoryLower.includes('casa') || productLower.includes('mesa') ||
           productLower.includes('cadeira') || productLower.includes('sofá')) {

    suggestions.push(
      {
        category: 'assembly',
        title: 'Montagem e Instalação',
        description: `Guia de montagem, ferramentas necessárias e tempo estimado para o ${productName}`,
        priority: 'high'
      },
      {
        category: 'care',
        title: 'Manutenção e Conservação',
        description: `Cuidados específicos para manter o ${productName} em perfeito estado`,
        priority: 'medium'
      },
      {
        category: 'usage',
        title: 'Dicas de Decoração',
        description: `Como integrar o ${productName} na decoração e maximizar sua funcionalidade`,
        priority: 'medium'
      }
    );
  }

  // Universal suggestions for all products
  if (suggestions.length < 4) {
    suggestions.push({
      category: 'usage',
      title: 'Dicas de Utilização',
      description: `Conselhos práticos para otimizar o uso e prolongar a vida útil do ${productName}`,
      priority: 'medium'
    });
  }

  suggestions.push({
    category: 'warranty',
    title: 'Garantia e Suporte',
    description: `Informações sobre garantia, assistência técnica e suporte específico para o ${productName}`,
    priority: 'low'
  });

  return {
    suggestions: suggestions.slice(0, 6),
    analysis: {
      productType: `${category} - ${productName}`,
      detectedFeatures,
      recommendedSections: suggestions.filter(s => s.priority === 'high').map(s => s.category)
    }
  };
}

// Generate content for selected additional info sections using AI
async function generateAdditionalInfoContent(
  productName: string,
  category: string,
  existingContent: string,
  selectedSections: string[]
): Promise<{ [key: string]: string }> {
  const content: { [key: string]: string } = {};

  // Process each section with AI-powered generation
  for (const section of selectedSections) {
    try {
      switch (section) {
        case 'specifications':
          content[section] = await generateSpecifications(productName, category, existingContent);
          break;
        case 'care':
          content[section] = await generateCareInstructions(productName, category, existingContent);
          break;
        case 'compatibility':
          content[section] = await generateCompatibilityInfo(productName, category, existingContent);
          break;
        case 'usage':
          content[section] = await generateUsageTips(productName, category, existingContent);
          break;
        case 'safety':
          content[section] = await generateSafetyInfo(productName, category, existingContent);
          break;
        case 'warranty':
          content[section] = await generateWarrantyInfo(productName, category, existingContent);
          break;
        case 'assembly':
          content[section] = await generateAssemblyInfo(productName, category, existingContent);
          break;
        case 'sizing':
          content[section] = await generateSizingInfo(productName, category, existingContent);
          break;
        default:
          // For unknown sections, generate intelligent content based on product analysis
          content[section] = await generateCustomSection(productName, category, existingContent, section);
      }
    } catch (error) {
      console.error(`Erro ao gerar conteúdo para ${section}:`, error);
      // Fallback to a basic message if AI generation fails
      content[section] = `Informações específicas sobre ${section} para ${productName} (categoria: ${category}).`;
    }
  }

  return content;
}

// Generate custom section content using AI
async function generateCustomSection(productName: string, category: string, existingContent: string, sectionName: string): Promise<string> {
  if (!openai) {
    return `Informações específicas sobre ${sectionName} para ${productName} da categoria ${category}.`;
  }

  try {
    const prompt = `Analise o produto "${productName}" da categoria "${category}" e gere informações específicas sobre "${sectionName}".

Conteúdo existente do produto: ${existingContent}

Instruções:
- Analise o tipo específico de produto e suas características
- Gere informações relevantes sobre ${sectionName} para este produto específico
- Evite informações genéricas que se aplicam a qualquer produto
- Use linguagem clara e informativa
- Máximo 5 linhas, uma informação por linha
- Foque em informações úteis e específicas

Responda apenas com as informações sobre ${sectionName}, sem introdução.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em produtos e informações técnicas. Gere conteúdo específico e relevante baseado no produto analisado."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.4,
      max_tokens: 200,
    });

    return response.choices[0].message.content?.trim() || `Informações específicas sobre ${sectionName} para ${productName}.`;
  } catch (error) {
    console.error(`Erro ao gerar seção customizada ${sectionName}:`, error);
    return `Informações específicas sobre ${sectionName} para ${productName} da categoria ${category}.`;
  }
}

// Intelligent mock functions for fallback
function generateIntelligentSpecificationsMock(productName: string, category: string, existingContent: string): string {
  const productLower = productName.toLowerCase();
  const categoryLower = category.toLowerCase();

  // Analyze product type and generate relevant specifications
  const specs: string[] = [];

  // Electronics
  if (categoryLower.includes('eletrónica') || categoryLower.includes('eletrônica') ||
      productLower.includes('smartphone') || productLower.includes('tablet') ||
      productLower.includes('computador') || productLower.includes('câmara')) {
    if (productLower.includes('smartphone') || productLower.includes('telemóvel')) {
      specs.push('Ecrã: Display de alta resolução com tecnologia avançada');
      specs.push('Bateria: Autonomia otimizada para uso prolongado');
      specs.push('Câmara: Sistema fotográfico de qualidade profissional');
      specs.push('Conectividade: 5G, Wi-Fi 6, Bluetooth 5.0');
      specs.push('Armazenamento: Capacidade expansível com desempenho rápido');
    } else if (productLower.includes('tablet')) {
      specs.push('Ecrã: Display touchscreen de alta definição');
      specs.push('Processador: Chip de alto desempenho para multitarefas');
      specs.push('Bateria: Até 10 horas de autonomia');
      specs.push('Conectividade: Wi-Fi, Bluetooth, opcional 4G/5G');
      specs.push('Peso: Design leve e portátil para uso móvel');
    } else {
      specs.push('Tecnologia: Componentes de última geração');
      specs.push('Conectividade: Múltiplas opções de ligação');
      specs.push('Eficiência: Consumo energético otimizado');
      specs.push('Compatibilidade: Suporte para diversos sistemas');
      specs.push('Durabilidade: Construção robusta e fiável');
    }
  }
  // Furniture
  else if (categoryLower.includes('mobiliário') || categoryLower.includes('móveis') ||
           productLower.includes('mesa') || productLower.includes('cadeira') ||
           productLower.includes('sofá') || productLower.includes('armário')) {
    if (productLower.includes('mesa')) {
      specs.push('Material: Madeira maciça/MDF de alta qualidade');
      specs.push('Dimensões: Tamanho otimizado para o espaço');
      specs.push('Acabamento: Verniz resistente a riscos e manchas');
      specs.push('Estrutura: Base estável com design ergonómico');
      specs.push('Montagem: Sistema de encaixe simplificado');
    } else if (productLower.includes('cadeira')) {
      specs.push('Ergonomia: Design anatómico para conforto prolongado');
      specs.push('Material: Estrutura resistente com estofado de qualidade');
      specs.push('Ajustes: Altura e inclinação reguláveis');
      specs.push('Base: Sistema de rodízios suaves e duráveis');
      specs.push('Peso suportado: Capacidade até 120kg');
    } else {
      specs.push('Material: Madeira/MDF de qualidade superior');
      specs.push('Acabamento: Resistente ao desgaste diário');
      specs.push('Design: Funcional e esteticamente atrativo');
      specs.push('Estrutura: Construção robusta e durável');
      specs.push('Montagem: Instruções claras e ferragens incluídas');
    }
  }
  // Clothing
  else if (categoryLower.includes('roupa') || categoryLower.includes('vestuário') ||
           productLower.includes('camisa') || productLower.includes('calças') ||
           productLower.includes('vestido') || productLower.includes('casaco')) {
    specs.push('Composição: Tecidos de alta qualidade e conforto');
    specs.push('Tamanhos: Disponível do XS ao XXL');
    specs.push('Corte: Design moderno e ajuste perfeito');
    specs.push('Acabamentos: Costuras reforçadas e detalhes cuidados');
    specs.push('Cuidados: Fácil manutenção e lavagem');
  }
  // Default for other categories
  else {
    specs.push('Qualidade: Materiais premium selecionados');
    specs.push('Design: Funcionalidade e estética equilibradas');
    specs.push('Durabilidade: Construção robusta para uso prolongado');
    specs.push('Acabamento: Detalhes cuidados e precisos');
    specs.push('Garantia: Conformidade com padrões de qualidade');
  }

  return specs.join('\n');
}

function generateIntelligentCareMock(productName: string, category: string, existingContent: string): string {
  const productLower = productName.toLowerCase();
  const categoryLower = category.toLowerCase();

  const instructions: string[] = [];

  // Electronics
  if (categoryLower.includes('eletrónica') || categoryLower.includes('eletrônica') ||
      productLower.includes('smartphone') || productLower.includes('tablet') ||
      productLower.includes('computador')) {
    instructions.push('Limpar com pano macio e seco, evitar produtos químicos');
    instructions.push('Proteger de humidade, calor excessivo e quedas');
    instructions.push('Usar capas protetoras e películas de ecrã');
    instructions.push('Carregar com carregadores originais ou certificados');
    instructions.push('Realizar atualizações de software regularmente');
  }
  // Furniture
  else if (categoryLower.includes('mobiliário') || categoryLower.includes('móveis')) {
    if (productLower.includes('madeira') || existingContent.includes('madeira')) {
      instructions.push('Limpar com pano húmido e detergente neutro');
      instructions.push('Aplicar cera ou óleo específico para madeira mensalmente');
      instructions.push('Evitar exposição solar direta e humidade excessiva');
      instructions.push('Verificar parafusos e fixações semestralmente');
      instructions.push('Usar bases protetoras para evitar riscos no chão');
    } else {
      instructions.push('Limpar com pano húmido e detergente suave');
      instructions.push('Evitar produtos abrasivos que danifiquem o acabamento');
      instructions.push('Proteger de exposição solar direta prolongada');
      instructions.push('Verificar estabilidade e fixações periodicamente');
      instructions.push('Manter em ambiente com humidade controlada');
    }
  }
  // Clothing
  else if (categoryLower.includes('roupa') || categoryLower.includes('vestuário')) {
    instructions.push('Seguir etiqueta de lavagem para temperatura adequada');
    instructions.push('Separar cores claras e escuras na lavagem');
    instructions.push('Secar à sombra para preservar cores e tecido');
    instructions.push('Passar a ferro na temperatura recomendada');
    instructions.push('Guardar em cabides ou dobrado adequadamente');
  }
  // Default care instructions
  else {
    instructions.push('Seguir instruções específicas do fabricante');
    instructions.push('Realizar limpeza regular com produtos adequados');
    instructions.push('Armazenar em condições apropriadas de temperatura');
    instructions.push('Verificar estado e realizar manutenção preventiva');
    instructions.push('Contactar suporte técnico em caso de dúvidas');
  }

  return instructions.join('\n');
}

function generateIntelligentCompatibilityMock(productName: string, category: string, existingContent: string): string {
  const productLower = productName.toLowerCase();
  const categoryLower = category.toLowerCase();

  const compatibility: string[] = [];

  // Electronics
  if (categoryLower.includes('eletrónica') || categoryLower.includes('eletrônica') ||
      productLower.includes('smartphone') || productLower.includes('tablet') ||
      productLower.includes('computador')) {
    if (productLower.includes('smartphone') || productLower.includes('telemóvel')) {
      compatibility.push('Compatível com redes 4G/5G de todas as operadoras');
      compatibility.push('Suporte para Android Auto e Apple CarPlay');
      compatibility.push('Compatibilidade com carregadores wireless Qi');
      compatibility.push('Funciona com assistentes de voz (Google, Siri)');
      compatibility.push('Sincronização com dispositivos wearables');
    } else if (productLower.includes('tablet')) {
      compatibility.push('Compatível com stylus e teclados externos');
      compatibility.push('Suporte para aplicações de produtividade');
      compatibility.push('Conectividade com projetores e monitores externos');
      compatibility.push('Sincronização com serviços de cloud');
      compatibility.push('Compatível com acessórios de montagem');
    } else {
      compatibility.push('Compatível com sistemas Windows, macOS e Linux');
      compatibility.push('Suporte para conectividade USB-C e Bluetooth');
      compatibility.push('Funciona com software de terceiros');
      compatibility.push('Integração com ecosistemas digitais');
      compatibility.push('Atualizações de firmware regulares');
    }
  }
  // Furniture
  else if (categoryLower.includes('mobiliário') || categoryLower.includes('móveis')) {
    compatibility.push('Adequado para diversos tipos de decoração');
    compatibility.push('Compatível com sistemas de organização modulares');
    compatibility.push('Adapta-se a diferentes tamanhos de espaço');
    compatibility.push('Coordena com outras peças da mesma linha');
    compatibility.push('Suporte para acessórios e complementos');
  }
  // Clothing
  else if (categoryLower.includes('roupa') || categoryLower.includes('vestuário')) {
    compatibility.push('Combina com diversos estilos e ocasiões');
    compatibility.push('Adequado para diferentes estações do ano');
    compatibility.push('Compatível com acessórios variados');
    compatibility.push('Adapta-se a diferentes tipos de corpo');
    compatibility.push('Coordena com outras peças do guarda-roupa');
  }
  // Default
  else {
    compatibility.push('Compatível com acessórios e complementos padrão');
    compatibility.push('Adequado para diversos ambientes de uso');
    compatibility.push('Funciona com sistemas e equipamentos convencionais');
    compatibility.push('Suporte para expansões e melhorias');
    compatibility.push('Integração com produtos similares');
  }

  return compatibility.join('\n');
}

function generateIntelligentUsageMock(productName: string, category: string, existingContent: string): string {
  const productLower = productName.toLowerCase();
  const categoryLower = category.toLowerCase();

  const tips: string[] = [];

  // Electronics
  if (categoryLower.includes('eletrónica') || categoryLower.includes('eletrônica') ||
      productLower.includes('smartphone') || productLower.includes('tablet') ||
      productLower.includes('computador')) {
    if (productLower.includes('smartphone') || productLower.includes('telemóvel')) {
      tips.push('Otimize a bateria ativando o modo poupança de energia');
      tips.push('Use gestos e atalhos para navegação mais rápida');
      tips.push('Configure notificações inteligentes para maior produtividade');
      tips.push('Aproveite funcionalidades de câmara em diferentes modos');
      tips.push('Mantenha aplicações atualizadas para melhor desempenho');
    } else if (productLower.includes('tablet')) {
      tips.push('Use o modo split-screen para multitarefas eficientes');
      tips.push('Configure perfis de utilizador para diferentes usos');
      tips.push('Aproveite aplicações de desenho e criatividade');
      tips.push('Use como segundo ecrã para maior produtividade');
      tips.push('Configure controlos parentais se necessário');
    } else {
      tips.push('Configure atalhos personalizados para tarefas frequentes');
      tips.push('Mantenha drivers e software sempre atualizados');
      tips.push('Use ferramentas de monitorização para otimizar desempenho');
      tips.push('Configure backups automáticos dos dados importantes');
      tips.push('Aproveite funcionalidades avançadas específicas do produto');
    }
  }
  // Furniture
  else if (categoryLower.includes('mobiliário') || categoryLower.includes('móveis')) {
    if (productLower.includes('mesa')) {
      tips.push('Use bases protetoras para evitar riscos e marcas');
      tips.push('Posicione longe de fontes diretas de calor');
      tips.push('Aproveite gavetas e compartimentos para organização');
      tips.push('Ajuste altura se for regulável para maior conforto');
      tips.push('Combine com cadeiras ergonómicas adequadas');
    } else if (productLower.includes('cadeira')) {
      tips.push('Ajuste altura para que pés fiquem apoiados no chão');
      tips.push('Configure apoio lombar para postura correta');
      tips.push('Faça pausas regulares para evitar fadiga');
      tips.push('Use apoios de braços na altura adequada');
      tips.push('Mantenha rodízios limpos para movimento suave');
    } else {
      tips.push('Distribua peso uniformemente para maior durabilidade');
      tips.push('Use o espaço de armazenamento de forma organizada');
      tips.push('Posicione em local com boa ventilação');
      tips.push('Combine com outros móveis para harmonia visual');
      tips.push('Aproveite funcionalidades modulares se disponíveis');
    }
  }
  // Clothing
  else if (categoryLower.includes('roupa') || categoryLower.includes('vestuário')) {
    tips.push('Combine com acessórios para looks mais elaborados');
    tips.push('Experimente diferentes formas de usar a peça');
    tips.push('Adapte o look para diferentes ocasiões');
    tips.push('Use camadas para versatilidade em diferentes climas');
    tips.push('Invista em peças básicas que combinem facilmente');
  }
  // Default
  else {
    tips.push('Explore todas as funcionalidades disponíveis gradualmente');
    tips.push('Mantenha em condições ideais para maior durabilidade');
    tips.push('Use conforme recomendações para melhor resultado');
    tips.push('Combine com acessórios adequados quando aplicável');
    tips.push('Aproveite ao máximo as características únicas do produto');
  }

  return tips.join('\n');
}

function generateIntelligentSafetyMock(productName: string, category: string, existingContent: string): string {
  const productLower = productName.toLowerCase();
  const categoryLower = category.toLowerCase();

  const safety: string[] = [];

  // Electronics
  if (categoryLower.includes('eletrónica') || categoryLower.includes('eletrônica') ||
      productLower.includes('smartphone') || productLower.includes('tablet') ||
      productLower.includes('computador')) {
    safety.push('Certificação CE e FCC para segurança eletromagnética');
    safety.push('Proteção contra sobrecarga e curto-circuito');
    safety.push('Materiais livres de substâncias tóxicas (RoHS)');
    safety.push('Testes de resistência a quedas e impactos');
    safety.push('Proteção de dados com encriptação avançada');
  }
  // Furniture
  else if (categoryLower.includes('mobiliário') || categoryLower.includes('móveis')) {
    if (productLower.includes('criança') || existingContent.includes('criança')) {
      safety.push('Cantos arredondados para segurança infantil');
      safety.push('Tintas e vernizes atóxicos e seguros');
      safety.push('Estrutura estável anti-tombamento');
      safety.push('Certificação de segurança para móveis infantis');
      safety.push('Ausência de peças pequenas destacáveis');
    } else {
      safety.push('Estrutura testada para cargas máximas especificadas');
      safety.push('Materiais com baixa emissão de formaldeído');
      safety.push('Acabamentos resistentes a riscos e impactos');
      safety.push('Fixações seguras e duráveis');
      safety.push('Conformidade com normas europeias de mobiliário');
    }
  }
  // Clothing
  else if (categoryLower.includes('roupa') || categoryLower.includes('vestuário')) {
    safety.push('Tecidos testados para alergias e irritações');
    safety.push('Corantes seguros e não tóxicos');
    safety.push('Costuras reforçadas para evitar rasgos');
    safety.push('Botões e acessórios bem fixados');
    safety.push('Certificação Oeko-Tex para segurança têxtil');
  }
  // Default
  else {
    safety.push('Materiais testados para segurança e durabilidade');
    safety.push('Conformidade com regulamentações europeias');
    safety.push('Testes de qualidade em laboratórios certificados');
    safety.push('Instruções de segurança claras incluídas');
    safety.push('Suporte técnico para questões de segurança');
  }

  return safety.join('\n');
}

function generateIntelligentWarrantyMock(productName: string, category: string, existingContent: string): string {
  const productLower = productName.toLowerCase();
  const categoryLower = category.toLowerCase();

  const warranty: string[] = [];

  // Electronics
  if (categoryLower.includes('eletrónica') || categoryLower.includes('eletrônica') ||
      productLower.includes('smartphone') || productLower.includes('tablet') ||
      productLower.includes('computador')) {
    warranty.push('Garantia de 2 anos contra defeitos de fabrico');
    warranty.push('Cobertura de componentes eletrónicos e software');
    warranty.push('Assistência técnica autorizada em todo o país');
    warranty.push('Substituição rápida em caso de defeito comprovado');
    warranty.push('Suporte técnico telefónico e online gratuito');
  }
  // Furniture
  else if (categoryLower.includes('mobiliário') || categoryLower.includes('móveis')) {
    warranty.push('Garantia de 3 anos para estrutura e mecanismos');
    warranty.push('Cobertura contra defeitos de material e montagem');
    warranty.push('Peças de reposição disponíveis por 5 anos');
    warranty.push('Assistência para problemas de montagem');
    warranty.push('Garantia estendida disponível mediante registo');
  }
  // Clothing
  else if (categoryLower.includes('roupa') || categoryLower.includes('vestuário')) {
    warranty.push('Garantia de 6 meses contra defeitos de confeção');
    warranty.push('Cobertura para costuras, fechos e botões');
    warranty.push('Política de troca por defeitos de fabrico');
    warranty.push('Garantia de qualidade dos tecidos e cores');
    warranty.push('Atendimento especializado para reclamações');
  }
  // Default
  else {
    warranty.push('Garantia conforme legislação portuguesa aplicável');
    warranty.push('Cobertura contra defeitos de fabrico e material');
    warranty.push('Assistência técnica especializada disponível');
    warranty.push('Peças de substituição mediante disponibilidade');
    warranty.push('Contacto direto para resolução de problemas');
  }

  return warranty.join('\n');
}

// AI-powered helper functions for generating specific content types
async function generateSpecifications(productName: string, category: string, existingContent: string = ''): Promise<string> {
  if (!openai) {
    // Fallback to intelligent mock based on product analysis
    return generateIntelligentSpecificationsMock(productName, category, existingContent);
  }

  try {
    const prompt = `Analise o produto "${productName}" da categoria "${category}" e gere especificações técnicas específicas e relevantes.

Conteúdo existente do produto: ${existingContent}

Instruções:
- Analise o tipo específico de produto e suas características
- Gere especificações técnicas relevantes para este produto específico
- Evite informações genéricas como "consultar ficha técnica"
- Inclua dimensões, materiais, peso, e outras especificações pertinentes
- Use linguagem técnica mas acessível
- Máximo 5 linhas, uma especificação por linha
- Formato: "Especificação: Valor/Descrição"

Responda apenas com as especificações, sem introdução.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em especificações técnicas de produtos. Gere especificações específicas e relevantes baseadas no produto analisado."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 200,
    });

    return response.choices[0].message.content?.trim() || generateIntelligentSpecificationsMock(productName, category, existingContent);
  } catch (error) {
    console.error('Erro ao gerar especificações:', error);
    return generateIntelligentSpecificationsMock(productName, category, existingContent);
  }
}

async function generateCareInstructions(productName: string, category: string, existingContent: string = ''): Promise<string> {
  if (!openai) {
    return generateIntelligentCareMock(productName, category, existingContent);
  }

  try {
    const prompt = `Analise o produto "${productName}" da categoria "${category}" e gere instruções de cuidado específicas.

Conteúdo existente do produto: ${existingContent}

Instruções:
- Analise o material e tipo específico do produto
- Gere instruções de cuidado relevantes para este produto específico
- Inclua limpeza, manutenção, armazenamento específicos
- Evite instruções genéricas que se aplicam a qualquer produto
- Use linguagem clara e prática
- Máximo 5 linhas, uma instrução por linha
- Foque em cuidados específicos do tipo de produto

Responda apenas com as instruções, sem introdução.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em cuidados e manutenção de produtos. Gere instruções específicas baseadas no tipo de produto analisado."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 200,
    });

    return response.choices[0].message.content?.trim() || generateIntelligentCareMock(productName, category, existingContent);
  } catch (error) {
    console.error('Erro ao gerar instruções de cuidado:', error);
    return generateIntelligentCareMock(productName, category, existingContent);
  }
}

async function generateCompatibilityInfo(productName: string, category: string, existingContent: string = ''): Promise<string> {
  if (!openai) {
    return generateIntelligentCompatibilityMock(productName, category, existingContent);
  }

  try {
    const prompt = `Analise o produto "${productName}" da categoria "${category}" e gere informações de compatibilidade específicas.

Conteúdo existente do produto: ${existingContent}

Instruções:
- Analise o tipo específico de produto e suas necessidades de compatibilidade
- Gere informações relevantes sobre compatibilidade para este produto específico
- Inclua sistemas, dispositivos, ou requisitos específicos do produto
- Evite informações genéricas sobre "múltiplas plataformas"
- Use linguagem técnica mas clara
- Máximo 5 linhas, uma informação por linha
- Foque em compatibilidades reais e específicas

Responda apenas com as informações de compatibilidade, sem introdução.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em compatibilidade de produtos. Gere informações específicas baseadas no tipo de produto analisado."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 200,
    });

    return response.choices[0].message.content?.trim() || generateIntelligentCompatibilityMock(productName, category, existingContent);
  } catch (error) {
    console.error('Erro ao gerar informações de compatibilidade:', error);
    return generateIntelligentCompatibilityMock(productName, category, existingContent);
  }
}

async function generateUsageTips(productName: string, category: string, existingContent: string = ''): Promise<string> {
  if (!openai) {
    return generateIntelligentUsageMock(productName, category, existingContent);
  }

  try {
    const prompt = `Analise o produto "${productName}" da categoria "${category}" e gere dicas de uso específicas e práticas.

Conteúdo existente do produto: ${existingContent}

Instruções:
- Analise o tipo específico de produto e como é usado
- Gere dicas práticas e específicas para este produto
- Inclua dicas de otimização, melhores práticas, e truques úteis
- Evite dicas genéricas como "ler o manual"
- Use linguagem prática e acessível
- Máximo 5 linhas, uma dica por linha
- Foque em valor real para o utilizador

Responda apenas com as dicas de uso, sem introdução.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em usabilidade de produtos. Gere dicas práticas e específicas baseadas no produto analisado."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.4,
      max_tokens: 200,
    });

    return response.choices[0].message.content?.trim() || generateIntelligentUsageMock(productName, category, existingContent);
  } catch (error) {
    console.error('Erro ao gerar dicas de uso:', error);
    return generateIntelligentUsageMock(productName, category, existingContent);
  }
}

async function generateSafetyInfo(productName: string, category: string, existingContent: string = ''): Promise<string> {
  if (!openai) {
    return generateIntelligentSafetyMock(productName, category, existingContent);
  }

  try {
    const prompt = `Analise o produto "${productName}" da categoria "${category}" e gere informações de segurança específicas.

Conteúdo existente do produto: ${existingContent}

Instruções:
- Analise os riscos específicos associados a este tipo de produto
- Gere informações de segurança relevantes e específicas
- Inclua precauções, certificações, e cuidados específicos
- Evite informações genéricas sobre "certificação CE"
- Use linguagem clara e responsável
- Máximo 5 linhas, uma informação por linha
- Foque em segurança real e específica do produto

Responda apenas com as informações de segurança, sem introdução.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em segurança de produtos. Gere informações específicas baseadas nos riscos do produto analisado."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.2,
      max_tokens: 200,
    });

    return response.choices[0].message.content?.trim() || generateIntelligentSafetyMock(productName, category, existingContent);
  } catch (error) {
    console.error('Erro ao gerar informações de segurança:', error);
    return generateIntelligentSafetyMock(productName, category, existingContent);
  }
}

async function generateWarrantyInfo(productName: string, category: string, existingContent: string = ''): Promise<string> {
  if (!openai) {
    return generateIntelligentWarrantyMock(productName, category, existingContent);
  }

  try {
    const prompt = `Analise o produto "${productName}" da categoria "${category}" e gere informações de garantia específicas.

Conteúdo existente do produto: ${existingContent}

Instruções:
- Analise o tipo de produto e suas necessidades de garantia típicas
- Gere informações de garantia relevantes para este produto específico
- Inclua prazos típicos, cobertura, e condições específicas
- Evite informações genéricas sobre "legislação portuguesa"
- Use linguagem clara e informativa
- Máximo 5 linhas, uma informação por linha
- Foque em garantias reais e específicas do tipo de produto

Responda apenas com as informações de garantia, sem introdução.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em garantias de produtos. Gere informações específicas baseadas no tipo de produto analisado."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 200,
    });

    return response.choices[0].message.content?.trim() || generateIntelligentWarrantyMock(productName, category, existingContent);
  } catch (error) {
    console.error('Erro ao gerar informações de garantia:', error);
    return generateIntelligentWarrantyMock(productName, category, existingContent);
  }
}

async function generateAssemblyInfo(productName: string, category: string, existingContent: string = ''): Promise<string> {
  if (!openai) {
    return generateIntelligentAssemblyMock(productName, category, existingContent);
  }

  try {
    const prompt = `Analise o produto "${productName}" da categoria "${category}" e gere informações de montagem específicas.

Conteúdo existente do produto: ${existingContent}

Instruções:
- Analise se o produto requer montagem e que tipo
- Gere informações específicas sobre montagem para este produto
- Inclua ferramentas, tempo estimado, e dificuldade específicos
- Evite informações genéricas sobre "manual incluído"
- Use linguagem clara e prática
- Máximo 5 linhas, uma informação por linha
- Foque em informações reais de montagem

Responda apenas com as informações de montagem, sem introdução.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em montagem de produtos. Gere informações específicas baseadas no tipo de produto analisado."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 200,
    });

    return response.choices[0].message.content?.trim() || generateIntelligentAssemblyMock(productName, category, existingContent);
  } catch (error) {
    console.error('Erro ao gerar informações de montagem:', error);
    return generateIntelligentAssemblyMock(productName, category, existingContent);
  }
}

async function generateSizingInfo(productName: string, category: string, existingContent: string = ''): Promise<string> {
  if (!openai) {
    return generateIntelligentSizingMock(productName, category, existingContent);
  }

  try {
    const prompt = `Analise o produto "${productName}" da categoria "${category}" e gere informações de tamanhos específicas.

Conteúdo existente do produto: ${existingContent}

Instruções:
- Analise se o produto tem variações de tamanho relevantes
- Gere informações específicas sobre tamanhos para este produto
- Inclua medidas, guias de escolha, e comparações específicas
- Evite informações genéricas sobre "tabela disponível"
- Use linguagem clara e útil
- Máximo 5 linhas, uma informação por linha
- Foque em informações reais de dimensionamento

Responda apenas com as informações de tamanhos, sem introdução.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em dimensionamento de produtos. Gere informações específicas baseadas no tipo de produto analisado."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 200,
    });

    return response.choices[0].message.content?.trim() || generateIntelligentSizingMock(productName, category, existingContent);
  } catch (error) {
    console.error('Erro ao gerar informações de tamanhos:', error);
    return generateIntelligentSizingMock(productName, category, existingContent);
  }
}

function generateIntelligentAssemblyMock(productName: string, category: string, existingContent: string): string {
  const productLower = productName.toLowerCase();
  const categoryLower = category.toLowerCase();

  const assembly: string[] = [];

  // Furniture
  if (categoryLower.includes('mobiliário') || categoryLower.includes('móveis') ||
      productLower.includes('mesa') || productLower.includes('cadeira') ||
      productLower.includes('armário') || productLower.includes('estante')) {
    if (productLower.includes('mesa')) {
      assembly.push('Montagem simples: apenas fixar pernas à tampo');
      assembly.push('Ferramentas necessárias: chave de fendas e chave inglesa');
      assembly.push('Tempo estimado: 15-30 minutos');
      assembly.push('Todas as ferragens e parafusos incluídos');
      assembly.push('Manual ilustrado passo-a-passo incluído');
    } else if (productLower.includes('cadeira')) {
      assembly.push('Montagem rápida: encaixe de componentes principais');
      assembly.push('Ferramentas: chave Allen incluída na embalagem');
      assembly.push('Tempo estimado: 10-20 minutos');
      assembly.push('Sistema de encaixe intuitivo sem parafusos');
      assembly.push('Instruções visuais claras na embalagem');
    } else {
      assembly.push('Montagem modular com sistema de encaixe');
      assembly.push('Kit de ferramentas básicas incluído');
      assembly.push('Tempo estimado: 45-90 minutos');
      assembly.push('Instruções detalhadas com diagramas');
      assembly.push('Suporte online para dúvidas de montagem');
    }
  }
  // Electronics
  else if (categoryLower.includes('eletrónica') || categoryLower.includes('eletrônica')) {
    assembly.push('Produto pré-montado, pronto para usar');
    assembly.push('Configuração inicial através de aplicação móvel');
    assembly.push('Guia de configuração rápida incluído');
    assembly.push('Vídeos tutoriais disponíveis online');
    assembly.push('Suporte técnico para configuração');
  }
  // Default
  else {
    assembly.push('Instruções de montagem/configuração incluídas');
    assembly.push('Ferramentas básicas necessárias listadas');
    assembly.push('Processo simplificado para facilitar montagem');
    assembly.push('Suporte disponível para dúvidas');
    assembly.push('Garantia cobre problemas de montagem');
  }

  return assembly.join('\n');
}

function generateIntelligentSizingMock(productName: string, category: string, existingContent: string): string {
  const productLower = productName.toLowerCase();
  const categoryLower = category.toLowerCase();

  const sizing: string[] = [];

  // Clothing
  if (categoryLower.includes('roupa') || categoryLower.includes('vestuário') ||
      productLower.includes('camisa') || productLower.includes('calças') ||
      productLower.includes('vestido') || productLower.includes('casaco')) {
    sizing.push('Tamanhos disponíveis: XS, S, M, L, XL, XXL');
    sizing.push('Guia de medidas: peito, cintura, anca detalhados');
    sizing.push('Corte regular com ajuste confortável');
    sizing.push('Recomendação: medir antes de escolher tamanho');
    sizing.push('Política de troca por tamanho inadequado');
  }
  // Furniture
  else if (categoryLower.includes('mobiliário') || categoryLower.includes('móveis')) {
    if (productLower.includes('mesa')) {
      sizing.push('Dimensões: Comprimento x Largura x Altura especificadas');
      sizing.push('Capacidade: Adequada para 4-6 pessoas');
      sizing.push('Espaço recomendado: Mínimo 2m x 2m');
      sizing.push('Altura padrão: 75cm para uso com cadeiras normais');
      sizing.push('Verificar medidas do espaço antes da compra');
    } else if (productLower.includes('cadeira')) {
      sizing.push('Altura do assento: 45-50cm (regulável se aplicável)');
      sizing.push('Largura do assento: Adequada para diferentes biótipos');
      sizing.push('Altura total: Compatível com mesas padrão');
      sizing.push('Peso suportado: Até 120kg com segurança');
      sizing.push('Dimensões compactas para facilitar arrumação');
    } else {
      sizing.push('Dimensões detalhadas na ficha técnica');
      sizing.push('Medidas externas e internas especificadas');
      sizing.push('Verificar compatibilidade com espaço disponível');
      sizing.push('Considerar espaço para abertura de portas/gavetas');
      sizing.push('Orientações para medição do espaço incluídas');
    }
  }
  // Electronics
  else if (categoryLower.includes('eletrónica') || categoryLower.includes('eletrônica')) {
    sizing.push('Dimensões compactas para portabilidade');
    sizing.push('Peso otimizado para uso móvel');
    sizing.push('Compatível com acessórios padrão');
    sizing.push('Tamanho ideal para uso quotidiano');
    sizing.push('Especificações técnicas detalhadas disponíveis');
  }
  // Default
  else {
    sizing.push('Dimensões otimizadas para o uso pretendido');
    sizing.push('Medidas detalhadas na descrição do produto');
    sizing.push('Comparação com objetos de referência');
    sizing.push('Orientações para escolha do tamanho adequado');
    sizing.push('Informações de dimensionamento na embalagem');
  }

  return sizing.join('\n');
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, productName, category, existingContent, selectedSections } = body;
    
    if (!productName || !category) {
      return NextResponse.json({ error: 'Nome do produto e categoria são obrigatórios.' }, { status: 400 });
    }
    
    if (action === 'suggest') {
      // Generate intelligent suggestions based on AI-powered product analysis
      const suggestions = await generateIntelligentAdditionalInfoSuggestions(productName, category, existingContent || '');
      return NextResponse.json(suggestions);
      
    } else if (action === 'generate') {
      // Generate content for selected sections using AI
      if (!selectedSections || !Array.isArray(selectedSections)) {
        return NextResponse.json({ error: 'Secções selecionadas são obrigatórias.' }, { status: 400 });
      }

      const content = await generateAdditionalInfoContent(productName, category, existingContent || '', selectedSections);
      return NextResponse.json({ content });
      
    } else {
      return NextResponse.json({ error: 'Ação inválida. Use "suggest" ou "generate".' }, { status: 400 });
    }
    
  } catch (error) {
    console.error('Erro na API de informações adicionais:', error);
    return NextResponse.json({ error: 'Falha ao processar o pedido.' }, { status: 500 });
  }
}
